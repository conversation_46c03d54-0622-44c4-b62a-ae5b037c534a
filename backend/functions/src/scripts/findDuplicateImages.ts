/* eslint-disable @typescript-eslint/no-unused-vars */
/* eslint-disable max-len */
import * as dotenv from "dotenv";
import * as admin from "firebase-admin";
import * as path from "path";
import * as fs from "fs";
import * as crypto from "crypto";

dotenv.config({ path: path.resolve(__dirname, "../../.env") });

import serviceAccount from "../firebaseServiceAccount";

// Initialisation de Firebase Admin SDK
admin.initializeApp({
    credential: admin.credential.cert(serviceAccount as admin.ServiceAccount),
});

interface ImageInfo {
    filePath: string;
    fileName: string;
    hash: string;
    size: number;
}

interface DuplicateGroup {
    hash: string;
    files: ImageInfo[];
    totalSize: number;
}

/**
 * This script scans the tmp directory for duplicate images using file hashing.
 * It identifies groups of identical images and reports them for manual review.
 * 
 * Alternative approaches for image comparison:
 * 1. Perceptual hashing (pHash) - detects visually similar images even with slight modifications
 * 2. Structural similarity (SSIM) - compares image structure and luminance
 * 3. Feature-based comparison using computer vision libraries
 * 
 * Current approach uses MD5 hashing which is fast and detects exact duplicates.
 */
async function findDuplicateImages() {
    try {
        console.log("Starting duplicate image detection...");
        
        const tmpDir = path.resolve(__dirname, "../tmp");
        
        if (!fs.existsSync(tmpDir)) {
            console.error(`Directory not found: ${tmpDir}`);
            console.log("Please create the tmp directory and add images to scan.");
            return;
        }

        console.log(`Scanning directory: ${tmpDir}`);
        
        // Get all image files
        const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.tif'];
        const imageFiles = getImageFiles(tmpDir, imageExtensions);
        
        if (imageFiles.length === 0) {
            console.log("No image files found in the tmp directory.");
            return;
        }

        console.log(`Found ${imageFiles.length} image files. Computing hashes...`);
        
        // Compute hashes for all images
        const imageInfos: ImageInfo[] = [];
        let processed = 0;
        
        for (const filePath of imageFiles) {
            try {
                const hash = await computeFileHash(filePath);
                const stats = fs.statSync(filePath);
                const fileName = path.basename(filePath);
                
                imageInfos.push({
                    filePath,
                    fileName,
                    hash,
                    size: stats.size
                });
                
                processed++;
                if (processed % 10 === 0) {
                    console.log(`Processed ${processed}/${imageFiles.length} files...`);
                }
            } catch (error) {
                console.error(`Error processing ${filePath}:`, error);
            }
        }

        console.log(`Successfully processed ${imageInfos.length} images.`);
        
        // Group by hash to find duplicates
        const hashGroups = new Map<string, ImageInfo[]>();
        
        for (const imageInfo of imageInfos) {
            if (!hashGroups.has(imageInfo.hash)) {
                hashGroups.set(imageInfo.hash, []);
            }
            hashGroups.get(imageInfo.hash)!.push(imageInfo);
        }
        
        // Find duplicate groups (more than 1 file with same hash)
        const duplicateGroups: DuplicateGroup[] = [];
        let totalDuplicates = 0;
        let totalWastedSpace = 0;
        
        for (const [hash, files] of hashGroups.entries()) {
            if (files.length > 1) {
                const totalSize = files.reduce((sum, file) => sum + file.size, 0);
                const wastedSpace = totalSize - files[0].size; // Keep one, remove others
                
                duplicateGroups.push({
                    hash,
                    files,
                    totalSize
                });
                
                totalDuplicates += files.length - 1; // -1 because we keep one
                totalWastedSpace += wastedSpace;
            }
        }
        
        // Report results
        console.log("\n" + "=".repeat(80));
        console.log("DUPLICATE DETECTION RESULTS");
        console.log("=".repeat(80));
        
        if (duplicateGroups.length === 0) {
            console.log("✅ No duplicate images found!");
        } else {
            console.log(`Found ${duplicateGroups.length} groups of duplicate images`);
            console.log(`Total duplicate files: ${totalDuplicates}`);
            console.log(`Total wasted space: ${formatBytes(totalWastedSpace)}`);
            console.log("\nDuplicate groups:\n");
            
            duplicateGroups.sort((a, b) => b.files.length - a.files.length);
            
            for (let i = 0; i < duplicateGroups.length; i++) {
                const group = duplicateGroups[i];
                console.log(`Group ${i + 1}: ${group.files.length} identical files (${formatBytes(group.files[0].size)} each)`);
                console.log(`Hash: ${group.hash}`);
                
                for (const file of group.files) {
                    console.log(`  - ${file.fileName}`);
                }
                
                const wastedSpace = (group.files.length - 1) * group.files[0].size;
                console.log(`  Wasted space: ${formatBytes(wastedSpace)}`);
                console.log("");
            }
            
            // Generate deletion commands for easy copy-paste
            console.log("=".repeat(80));
            console.log("SUGGESTED DELETION COMMANDS");
            console.log("=".repeat(80));
            console.log("# Keep the first file in each group, delete the rest:");
            console.log("");
            
            for (const group of duplicateGroups) {
                console.log(`# Group with ${group.files.length} files:`);
                for (let i = 1; i < group.files.length; i++) {
                    const relativePath = path.relative(tmpDir, group.files[i].filePath);
                    console.log(`rm "tmp/${relativePath}"`);
                }
                console.log("");
            }
        }
        
        // Save detailed report to file
        const reportPath = path.join(tmpDir, "duplicate-report.json");
        const report = {
            scanDate: new Date().toISOString(),
            totalFiles: imageFiles.length,
            duplicateGroups: duplicateGroups.length,
            totalDuplicates,
            totalWastedSpace,
            groups: duplicateGroups.map(group => ({
                hash: group.hash,
                fileCount: group.files.length,
                fileSize: group.files[0].size,
                files: group.files.map(f => ({
                    fileName: f.fileName,
                    relativePath: path.relative(tmpDir, f.filePath)
                }))
            }))
        };
        
        fs.writeFileSync(reportPath, JSON.stringify(report, null, 2));
        console.log(`\nDetailed report saved to: ${reportPath}`);
        
    } catch (error) {
        console.error("Error during duplicate detection:", error);
    } finally {
        // Close the Firebase connection
        await admin.app().delete();
    }
}

/**
 * Recursively get all image files from a directory
 */
function getImageFiles(dir: string, extensions: string[]): string[] {
    const files: string[] = [];
    
    function scanDirectory(currentDir: string) {
        const items = fs.readdirSync(currentDir);
        
        for (const item of items) {
            const fullPath = path.join(currentDir, item);
            const stat = fs.statSync(fullPath);
            
            if (stat.isDirectory()) {
                scanDirectory(fullPath);
            } else if (stat.isFile()) {
                const ext = path.extname(item).toLowerCase();
                if (extensions.includes(ext)) {
                    files.push(fullPath);
                }
            }
        }
    }
    
    scanDirectory(dir);
    return files;
}

/**
 * Compute MD5 hash of a file
 */
async function computeFileHash(filePath: string): Promise<string> {
    return new Promise((resolve, reject) => {
        const hash = crypto.createHash('md5');
        const stream = fs.createReadStream(filePath);
        
        stream.on('data', (data) => {
            hash.update(data);
        });
        
        stream.on('end', () => {
            resolve(hash.digest('hex'));
        });
        
        stream.on('error', (error) => {
            reject(error);
        });
    });
}

/**
 * Format bytes to human readable format
 */
function formatBytes(bytes: number): string {
    if (bytes === 0) return '0 Bytes';
    
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

// Execute the script
findDuplicateImages();
